"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Form as FormProvider } from '@/components/ui/form'
import { LocalSearchSelectField, RemoteSearchSelectField } from '@/components/forms'
import { commonOptions } from '@/hooks/use-select-options'

// Mock API function for remote search
const searchUsers = async (searchQuery?: string): Promise<Array<{ id: string; name: string; email: string }>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const mockUsers = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>' },
    { id: '4', name: '<PERSON>', email: '<EMAIL>' },
    { id: '5', name: '<PERSON> <PERSON>', email: '<EMAIL>' },
    { id: '6', name: '<PERSON> <PERSON>', email: '<EMAIL>' },
    { id: '7', name: '<PERSON>', email: '<EMAIL>' },
    { id: '8', name: '<PERSON> <PERSON>', email: '<EMAIL>' },
  ]
  
  if (!searchQuery || searchQuery.length < 2) {
    return mockUsers.slice(0, 3) // Return first 3 as default
  }
  
  return mockUsers.filter(user => 
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  )
}

// Extended options for local search
const extendedCountries = [
  ...commonOptions.countries,
  { value: 'es', label: 'Spain' },
  { value: 'it', label: 'Italy' },
  { value: 'nl', label: 'Netherlands' },
  { value: 'se', label: 'Sweden' },
  { value: 'no', label: 'Norway' },
  { value: 'dk', label: 'Denmark' },
  { value: 'fi', label: 'Finland' },
  { value: 'ch', label: 'Switzerland' },
  { value: 'at', label: 'Austria' },
  { value: 'be', label: 'Belgium' },
  { value: 'pt', label: 'Portugal' },
  { value: 'gr', label: 'Greece' },
  { value: 'pl', label: 'Poland' },
  { value: 'cz', label: 'Czech Republic' },
  { value: 'hu', label: 'Hungary' },
  { value: 'sk', label: 'Slovakia' },
  { value: 'ro', label: 'Romania' },
  { value: 'bg', label: 'Bulgaria' },
  { value: 'hr', label: 'Croatia' },
  { value: 'si', label: 'Slovenia' },
]

const formSchema = z.object({
  country: z.string().min(1, 'Please select a country'),
  assignedUser: z.string().min(1, 'Please select a user'),
})

type FormData = z.infer<typeof formSchema>

export const SplitSearchSelectExamples = () => {
  const [submittedData, setSubmittedData] = useState<FormData | null>(null)
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      country: '',
      assignedUser: '',
    },
  })

  const onSubmit = (data: FormData) => {
    console.log('Form submitted:', data)
    setSubmittedData(data)
  }

  const handleReset = () => {
    form.reset()
    setSubmittedData(null)
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold tracking-tight">Search Select Field Examples</h2>
        <p className="text-muted-foreground">
          Demonstrates local (client-side) and remote (API-based) search select fields.
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {/* Local Search Select Example */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Local Search Select
              <Badge variant="secondary" className="text-xs">
                Client-side
              </Badge>
            </CardTitle>
            <CardDescription>
              Filters a predefined list of options on the client-side. 
              Best for small datasets that don&apos;t change frequently.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                <strong>Features:</strong>
                <ul className="list-disc ml-4 mt-1">
                  <li>Instant filtering (no API calls)</li>
                  <li>Works offline</li>
                  <li>Searches both label and value</li>
                  <li>No minimum search length required</li>
                </ul>
              </div>
                <FormProvider {...form}>
                <form className="space-y-4">
                  <LocalSearchSelectField
                    name="country"
                    label="Country"
                    description="Select your country from the list"
                    placeholder="Choose a country"
                    searchPlaceholder="Search countries..."
                    options={extendedCountries}
                    clearable
                    required
                  />
                </form>
              </FormProvider>
            </div>
          </CardContent>
        </Card>

        {/* Remote Search Select Example */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Remote Search Select
              <Badge variant="secondary" className="text-xs">
                API-based
              </Badge>
            </CardTitle>
            <CardDescription>
              Fetches options from an API based on the search query. 
              Best for large datasets or dynamic content.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                <strong>Features:</strong>
                <ul className="list-disc ml-4 mt-1">
                  <li>Dynamic API-based search</li>
                  <li>Debounced search requests</li>
                  <li>Loading and error states</li>
                  <li>Configurable minimum search length</li>
                </ul>
              </div>
                <FormProvider {...form}>
                <form className="space-y-4">
                  <RemoteSearchSelectField
                    name="assignedUser"
                    label="Assigned User"
                    description="Search and select a user to assign"
                    placeholder="Search for a user"
                    searchPlaceholder="Type name or email..."
                    optionsConfig={{
                      apiConfig: {
                        queryKey: ['users'],
                        queryFn: searchUsers,
                        staleTime: 5 * 60 * 1000, // 5 minutes
                        cacheTime: 10 * 60 * 1000, // 10 minutes
                      },
                      transform: (users) => users.map(user => ({
                        value: user.id,
                        label: `${user.name} (${user.email})`,
                      })),
                    }}
                    minSearchLength={2}
                    searchDebounceMs={300}
                    clearable
                    required
                  />
                </form>
              </FormProvider>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Form Example */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Form Example</CardTitle>
          <CardDescription>
            A working form that uses both local and remote search select fields.
          </CardDescription>
        </CardHeader>
        <CardContent>          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <LocalSearchSelectField
                  name="country"
                  label="Country"
                  description="Select your country"
                  placeholder="Choose a country"
                  searchPlaceholder="Search countries..."
                  options={extendedCountries}
                  clearable
                  required
                />
                
                <RemoteSearchSelectField
                  name="assignedUser"
                  label="Assigned User"
                  description="Search and select a user"
                  placeholder="Search for a user"
                  searchPlaceholder="Type name or email..."
                  optionsConfig={{
                    apiConfig: {
                      queryKey: ['users'],
                      queryFn: searchUsers,
                    },
                    transform: (users) => users.map(user => ({
                      value: user.id,
                      label: `${user.name} (${user.email})`,
                    })),
                  }}
                  minSearchLength={2}
                  clearable
                  required
                />
              </div>
              
              <Separator />
              
              <div className="flex gap-2">
                <Button type="submit">Submit Form</Button>
                <Button type="button" variant="outline" onClick={handleReset}>
                  Reset
                </Button>
              </div>
            </form>
          </FormProvider>
          
          {submittedData && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">Submitted Data:</h3>
              <pre className="text-sm">
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Guide</CardTitle>
          <CardDescription>
            When to use each type of search select field.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-green-600 mb-2">Use Local Search Select when:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc ml-4">
                <li>You have a small, fixed set of options (&lt; 100 items)</li>
                <li>The data doesn&apos;t change frequently</li>
                <li>You want instant search results</li>
                <li>You need offline functionality</li>
                <li>The options can be bundled with your app</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-blue-600 mb-2">Use Remote Search Select when:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc ml-4">
                <li>You have a large dataset (&gt; 100 items)</li>
                <li>The data changes frequently</li>
                <li>You want to reduce initial bundle size</li>
                <li>You need real-time or fresh data</li>
                <li>The data is user-specific or filtered</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
