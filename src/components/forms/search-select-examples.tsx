"use client"

import React from 'react'
import { z } from 'zod'
import { useFormContext } from 'react-hook-form'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Loader2 } from 'lucide-react'
import {
  Form,
  RemoteSearchSelectField,
  LocalSearchSelectField,
  InputField,
} from './index'
import { useFormSubmission, simulateFormSubmission } from '@/hooks/use-form-submission'
import {
  searchUsers,
  searchProducts,
  searchCities,
  transformUsers,
  transformProducts,
  transformCities,
  SearchableUser,
  SearchableProduct,
  SearchableCity,
} from '@/lib/api/search-select-options'

// Form schema for search select examples
const searchFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  
  // Search select fields
  assignedUser: z.string().min(1, 'Please select a user'),
  favoriteProduct: z.string().min(1, 'Please select a product'),
  preferredCity: z.string().min(1, 'Please select a city'),
})

type SearchFormData = z.infer<typeof searchFormSchema>

// Submit button component that shows loading state
function SubmitButton() {
  const { formState } = useFormContext()
  const { isSubmitting } = formState

  return (
    <Button 
      type="submit" 
      disabled={isSubmitting}
      className="w-full"
    >
      {isSubmitting ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Submitting...
        </>
      ) : (
        'Submit Form'
      )}
    </Button>
  )
}

export function SearchSelectExamples() {
  const { submit: submitForm } = useFormSubmission<SearchFormData>({
    mutationFn: simulateFormSubmission,
    options: {
      onSuccess: (data, variables) => {
        console.log('Form submitted successfully:', data, variables)
      },
      onError: (error, variables) => {
        console.error('Form submission failed:', error, variables)
      },
    },
  })

  const handleSubmit = (data: SearchFormData) => {
    submitForm(data)
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Search Select Fields Example</CardTitle>
        <CardDescription>
          Demonstrates search select fields with API-based filtering, debounced search, 
          loading states, and error handling.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form
          schema={searchFormSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            name: '',
            email: '',
            assignedUser: '',
            favoriteProduct: '',
            preferredCity: '',
          }}
        >
          <div className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  name="name"
                  label="Full Name"
                  placeholder="Enter your full name"
                  required
                />
                <InputField
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <Separator />

            {/* Search Select Fields */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Search Select Fields</h3>
              
              <div className="grid grid-cols-1 gap-6">
                {/* User Search */}
                <RemoteSearchSelectField<SearchableUser[]>
                  name="assignedUser"
                  label="Assigned User"
                  placeholder="Select a user"
                  searchPlaceholder="Search users by name, email, or department..."
                  optionsConfig={{
                    apiConfig: {
                      queryKey: ['search-users'],
                      queryFn: searchUsers,
                    },
                    transform: transformUsers,
                  }}
                  description="Search and select a user from the organization (min 2 characters)"
                  clearable
                  required
                  minSearchLength={2}
                  searchDebounceMs={300}
                />

                {/* Product Search */}
                <RemoteSearchSelectField<SearchableProduct[]>
                  name="favoriteProduct"
                  label="Favorite Product"
                  placeholder="Select a product"
                  searchPlaceholder="Search products by name, brand, or category..."
                  optionsConfig={{
                    apiConfig: {
                      queryKey: ['search-products'],
                      queryFn: searchProducts,
                    },
                    transform: transformProducts,
                  }}
                  description="Search and select your favorite product (min 2 characters)"
                  clearable
                  required
                  minSearchLength={2}
                  searchDebounceMs={400}
                  noResultsText="No products found matching your search"
                />

                {/* City Search */}
                <RemoteSearchSelectField<SearchableCity[]>
                  name="preferredCity"
                  label="Preferred City"
                  placeholder="Select a city"
                  searchPlaceholder="Search cities by name or country..."
                  optionsConfig={{
                    apiConfig: {
                      queryKey: ['search-cities'],
                      queryFn: searchCities,
                    },
                    transform: transformCities,
                  }}
                  description="Search and select your preferred city (min 2 characters)"
                  clearable
                  required
                  minSearchLength={2}
                  searchDebounceMs={250}
                  maxHeight="300px"
                />
              </div>
            </div>

            <Separator />

            {/* Submit Button */}
            <div className="flex justify-end">
              <div className="w-full md:w-auto md:min-w-[200px]">
                <SubmitButton />
              </div>
            </div>
          </div>
        </Form>
      </CardContent>
    </Card>
  )
}

// Additional example with static options for comparison
export function StaticSearchSelectExample() {
  const staticOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3 (Disabled)', disabled: true },
    { value: 'option4', label: 'Option 4' },
  ]

  const simpleSchema = z.object({
    staticChoice: z.string().min(1, 'Please select an option'),
  })

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Static Search Select Example</CardTitle>
        <CardDescription>
          Example with static options for comparison
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form
          schema={simpleSchema}
          onSubmit={(data) => console.log('Static form data:', data)}
          defaultValues={{ staticChoice: '' }}
        >
          <LocalSearchSelectField
            name="staticChoice"
            label="Static Options"
            placeholder="Select from static options"
            searchPlaceholder="Type to filter options..."
            options={staticOptions}
            description="This uses static options with client-side filtering"
            clearable
            required
            minSearchLength={0}
          />
          
          <div className="mt-6">
            <Button type="submit" className="w-full">
              Submit Static Form
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  )
}
